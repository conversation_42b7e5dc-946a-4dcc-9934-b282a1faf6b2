{"version": 3, "file": "result.d.ts", "sourceRoot": "", "sources": ["../../../src/select-query-parser/result.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,eAAe,EAAE,MAAM,SAAS,CAAA;AAC5E,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,UAAU,CAAA;AAC1C,OAAO,EACL,kBAAkB,EAClB,oBAAoB,EACpB,aAAa,EACb,eAAe,EACf,QAAQ,EACR,cAAc,EACd,eAAe,EAChB,MAAM,SAAS,CAAA;AAChB,OAAO,EACL,8BAA8B,EAC9B,sBAAsB,EACtB,KAAK,EACL,kBAAkB,EAClB,aAAa,EACb,cAAc,EACd,mBAAmB,EACnB,gBAAgB,EACjB,MAAM,SAAS,CAAA;AAEhB;;;;;;;;GAQG;AACH,oBAAY,SAAS,CACnB,MAAM,SAAS,aAAa,EAC5B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,EACZ,aAAa,EACb,KAAK,SAAS,MAAM,IAClB,KAAK,CAAC,MAAM,CAAC,SAAS,IAAI,GAC1B,UAAU,CAAC,KAAK,CAAC,SAAS,MAAM,WAAW,GACzC,WAAW,SAAS,GAAG,CAAC,IAAI,EAAE,GAC5B,YAAY,SAAS,MAAM,GACzB,yBAAyB,CAAC,WAAW,CAAC,GACtC,GAAG,GACL,WAAW,GACb,GAAG,GACL,aAAa,SAAS,IAAI,GAC1B,UAAU,CAAC,KAAK,CAAC,SAAS,MAAM,WAAW,GACzC,WAAW,SAAS,GAAG,CAAC,IAAI,EAAE,GAC5B,YAAY,CAAC,WAAW,EAAE,YAAY,SAAS,MAAM,GAAG,YAAY,GAAG,UAAU,EAAE,GAAG,CAAC,GACvF,WAAW,GACb,GAAG,GACL,UAAU,CAAC,KAAK,CAAC,SAAS,MAAM,WAAW,GAC3C,WAAW,SAAS,GAAG,CAAC,IAAI,EAAE,GAC5B,YAAY,SAAS,MAAM,GACzB,aAAa,SAAS,mBAAmB,EAAE,GACzC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,CAAC,GACnE,gBAAgB,CAAC,gDAAgD,CAAC,GACpE,gBAAgB,CAAC,+CAA+C,CAAC,GACnE,WAAW,GACb,KAAK,CAAA;AAET,aAAK,+BAA+B,CAAC,KAAK,SAAS,GAAG,CAAC,SAAS,IAC9D,KAAK,CAAC,mBAAmB,CAAC,SAAS,kBAAkB,GACjD;KAGG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,eAAe,GAC3E,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAClC,MAAM;CACX,GACD;KAEG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,eAAe,GAC3E,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAClC,GAAG;CACR,CAAA;AAEP,aAAK,6BAA6B,CAAC,IAAI,SAAS,GAAG,CAAC,SAAS,IAAI,eAAe,CAC9E,IAAI,CAAC,UAAU,CAAC,CACjB,SAAS,IAAI,GACV;KACG,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,EAAE,GACpE,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAC7C,+BAA+B,CAAC,IAAI,CAAC;CAC1C,GACD,+BAA+B,CAAC,IAAI,CAAC,CAAA;AAEzC;;GAEG;AACH,aAAK,wBAAwB,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,IAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,GAC5E,GAAG,GACH,IAAI,SAAS,GAAG,CAAC,UAAU,GAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,QAAQ,EAAE,GAC/C,GAAG,GACH,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,SAAS,EAAE,GAClD;KACG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,SAAS,eAAe,GACzG,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAC9B,GAAG;CACR,GACD,GAAG,GACL,IAAI,SAAS,GAAG,CAAC,SAAS,GAC1B,6BAA6B,CAAC,IAAI,CAAC,GACnC,GAAG,CAAA;AAEP;;GAEG;AACH,aAAK,yBAAyB,CAC5B,KAAK,SAAS,GAAG,CAAC,IAAI,EAAE,EACxB,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,IACtC,KAAK,SAAS,CAAC,MAAM,SAAS,EAAE,GAAG,MAAM,SAAS,CAAC,GACnD,SAAS,SAAS,GAAG,CAAC,IAAI,GACxB,SAAS,SAAS,GAAG,CAAC,IAAI,EAAE,GAC1B,wBAAwB,CAAC,SAAS,CAAC,SAAS,MAAM,WAAW,GAC3D,WAAW,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACzC,yBAAyB,CAAC,SAAS,EAAE,GAAG,GAAG,WAAW,CAAC,GACvD,WAAW,GACb,GAAG,GACL,GAAG,GACL,GAAG,GACL,QAAQ,CAAC,GAAG,CAAC,CAAA;AAEjB;;;;;;GAMG;AACH,oBAAY,cAAc,CACxB,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,SAAS,MAAM,EAC3B,QAAQ,SAAS,GAAG,CAAC,IAAI,IACvB,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAC7C,GAAG,GACH,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,GAC9C,kBAAkB,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,GACvE,gBAAgB,CAAC,4BAA4B,CAAC,CAAA;AAElD;;GAEG;AACH,oBAAY,YAAY,CACtB,KAAK,SAAS,GAAG,CAAC,IAAI,EAAE,EACxB,YAAY,SAAS,MAAM,EAC3B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,IACtC,KAAK,SAAS,CAAC,MAAM,SAAS,EAAE,GAAG,MAAM,SAAS,CAAC,GACnD,SAAS,SAAS,GAAG,CAAC,IAAI,GACxB,SAAS,SAAS,GAAG,CAAC,IAAI,EAAE,GAC1B,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE,SAAS,CAAC,SAAS,MAAM,WAAW,GACpE,WAAW,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACzC,YAAY,CAAC,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,GAAG,WAAW,CAAC,GAC7D,WAAW,SAAS,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAC7C,gBAAgB,CAAC,CAAC,CAAC,GACnB,gBAAgB,CAAC,kDAAkD,CAAC,GACtE,gBAAgB,CAAC,yBAAyB,CAAC,GAC7C,gBAAgB,CAAC,sCAAsC,CAAC,GAC1D,gBAAgB,CAAC,gCAAgC,CAAC,GACpD,QAAQ,CAAC,GAAG,CAAC,CAAA;AAEjB;;;;;;;;;GASG;AACH,oBAAY,YAAY,CACtB,MAAM,SAAS,aAAa,EAC5B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,SAAS,MAAM,EAC3B,aAAa,SAAS,mBAAmB,EAAE,EAC3C,KAAK,SAAS,GAAG,CAAC,IAAI,EAAE,EACxB,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,IACtC,8BAA8B,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,CAAC,SAAS,KAAK,GACxF,KAAK,SAAS,CAAC,MAAM,SAAS,EAAE,GAAG,MAAM,SAAS,CAAC,GACjD,SAAS,SAAS,GAAG,CAAC,IAAI,GACxB,SAAS,SAAS,GAAG,CAAC,IAAI,EAAE,GAC1B,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,CAAC,SAAS,MAAM,WAAW,GACxF,WAAW,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACzC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,GAAG,WAAW,CAAC,GACpF,WAAW,SAAS,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAC7C,gBAAgB,CAAC,CAAC,CAAC,GACnB,gBAAgB,CAAC,kDAAkD,CAAC,GACtE,gBAAgB,CAAC,yBAAyB,CAAC,GAC7C,gBAAgB,CAAC,+CAA+C,CAAC,GACnE,gBAAgB,CAAC,yCAAyC,CAAC,GAC7D,QAAQ,CAAC,GAAG,CAAC,GACf,QAAQ,CAAC,8BAA8B,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAA;AAExF;;;;;;;;GAQG;AACH,oBAAY,WAAW,CACrB,MAAM,SAAS,aAAa,EAC5B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,SAAS,MAAM,EAC3B,aAAa,SAAS,mBAAmB,EAAE,EAC3C,QAAQ,SAAS,GAAG,CAAC,IAAI,IAGzB,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GACzC,GAAG,GACH,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,GAC/C,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,GAC9F,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,GAC9C,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,GAC5F,gBAAgB,CAAC,wBAAwB,CAAC,CAAA;AAEhD;;;;;;;;GAQG;AACH,aAAK,gBAAgB,CACnB,MAAM,SAAS,aAAa,EAC5B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,SAAS,MAAM,EAC3B,aAAa,SAAS,mBAAmB,EAAE,EAC3C,KAAK,SAAS,GAAG,CAAC,SAAS,IACzB,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE,GAC5B,EAAE,GACF,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,SAAS,IAAI,GAC/C,uBAAuB,CAAC,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,CAAC,GACnE,kBAAkB,CAAC,GAAG,EAAE,YAAY,EAAE,KAAK,CAAC,CAAA;AAEhD,aAAK,mBAAmB,CACtB,KAAK,EACL,IAAI,SAAS,MAAM,GAAG,SAAS,EAC/B,QAAQ,SAAS,eAAe,IAC9B,IAAI,SAAS,MAAM,GACnB,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,KAAK,GAEvC,eAAe,CAAC,QAAQ,CAAC,GACzB,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,MAAM,UAAU,GACpD,UAAU,SAAS,MAAM,GAEvB,UAAU,GACV,aAAa,CAAC,UAAU,CAAC,SAAS,IAAI,GAEtC,UAAU,GACV,QAAQ,SAAS,MAAM,GAEvB,UAAU,GAEV,eAAe,CAAC,QAAQ,CAAC,GAC3B,eAAe,CAAC,QAAQ,CAAC,GAE3B,eAAe,CAAC,QAAQ,CAAC,CAAA;AAE7B;;;;;;GAMG;AACH,aAAK,kBAAkB,CACrB,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,SAAS,MAAM,EAC3B,KAAK,SAAS,GAAG,CAAC,SAAS,IACzB,KAAK,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,GAAG,OAAO,GACzC,KAAK,CAAC,mBAAmB,CAAC,SAAS,kBAAkB,GACnD;KAGG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,eAAe,GAC3E,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAClC,MAAM;CACX,GACD;KAEG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,eAAe,GAC3E,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,GAC7E,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;CACvB,GACH,gBAAgB,CAAC,WAAW,KAAK,CAAC,MAAM,CAAC,wBAAwB,YAAY,IAAI,CAAC,CAAA;AAEtF;;;;;;;;GAQG;AACH,oBAAY,uBAAuB,CACjC,MAAM,SAAS,aAAa,EAC5B,aAAa,SAAS,mBAAmB,EAAE,EAC3C,KAAK,SAAS,GAAG,CAAC,SAAS,EAC3B,kBAAkB,SAAS,MAAM,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,IAC9D,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,kBAAkB,CAAC,SAAS,MAAM,QAAQ,GAC5F,QAAQ,SAAS;IACf,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,KAAK,GAAG,eAAe,CAAC,CAAA;IAC5D,QAAQ,EAAE,mBAAmB,GAAG;QAAE,KAAK,EAAE,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAA;KAAE,CAAA;IACtE,SAAS,EAAE,MAAM,CAAA;CAClB,GACC,6BAA6B,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,kBAAkB,CAAC,GAE1E;KAAG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,QAAQ;CAAE,GACpD;KACG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,iCAAiC,CAAC,GACvF,MAAM;CACT,CAAA;AAEL;;GAEG;AACH,aAAK,6BAA6B,CAChC,MAAM,SAAS,aAAa,EAC5B,QAAQ,SAAS;IACf,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,KAAK,GAAG,eAAe,CAAC,CAAA;IAC5D,QAAQ,EAAE,mBAAmB,GAAG;QAAE,KAAK,EAAE,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAA;KAAE,CAAA;IACtE,SAAS,EAAE,MAAM,CAAA;CAClB,EACD,KAAK,SAAS,GAAG,CAAC,SAAS,EAC3B,kBAAkB,SAAS,MAAM,cAAc,CAAC,MAAM,CAAC,IACrD,YAAY,CACd,MAAM,EACN,QAAQ,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,EAClC,KAAK,CAAC,MAAM,CAAC,EACb,QAAQ,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,EAC5C,KAAK,CAAC,UAAU,CAAC,SAAS,SAAS,GAC/B,EAAE,GACF,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,GAAG,CAAC,IAAI,EAAE,GACxD,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,GACrC,EAAE,CACP,SAAS,MAAM,iBAAiB,GAC7B;KACG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,SAAS,SAAS,GACzE,KAAK,SAAS;QAAE,SAAS,EAAE,IAAI,CAAA;KAAE,GAC/B,QAAQ,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,SAAS,IAAI,GAC7C,iBAAiB,GACjB,iBAAiB,EAAE,GACrB,QAAQ,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,SAAS,IAAI,GAC/C,iBAAiB,GAAG,IAAI,GACxB,iBAAiB,EAAE,GAEvB,QAAQ,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,SAAS,kBAAkB,GAGnE,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,KAAK,GACzC,kBAAkB,CAChB,cAAc,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,EAC1C,QAAQ,CAAC,UAAU,CAAC,CACrB,SAAS,IAAI,GACZ,iBAAiB,GAAG,IAAI,GACxB,iBAAiB,GAGnB,iBAAiB,EAAE,GAEvB,kBAAkB,CACd,cAAc,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,EAC1C,QAAQ,CAAC,UAAU,CAAC,CACrB,SAAS,IAAI,GACd,KAAK,SAAS;QAAE,SAAS,EAAE,IAAI,CAAA;KAAE,GAC/B,iBAAiB,GACjB,iBAAiB,GAAG,IAAI,GAC1B,iBAAiB;CACtB,GACD;KACG,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,4CAA4C,CAAC,GAClG,MAAM;CACT,CAAA;AAEL;;;;;;;;GAQG;AACH,aAAK,iBAAiB,CACpB,MAAM,SAAS,aAAa,EAC5B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,YAAY,SAAS,MAAM,EAC3B,aAAa,SAAS,mBAAmB,EAAE,EAC3C,MAAM,SAAS,GAAG,CAAC,UAAU,IAC3B,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,MAAM,MAAM,GAC5F,MAAM,SAAS,gBAAgB,CAAC,MAAM,CAAC,CAAC,GACtC,gBAAgB,CAAC,CAAC,CAAC,GACnB,oBAAoB,CAAC,MAAM,CAAC,SAAS,OAAO,EAAE,GAC9C;KACG,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,IAAI,YAAY,UAAU,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,4EAA4E,CAAC;CAClL,GACD,uBAAuB,CAAC,MAAM,CAAC,GACjC,KAAK,CAAA;AAET;;GAEG;AACH,aAAK,uBAAuB,CAAC,MAAM,IAAI,MAAM,SAAS,MAAM,CAC1D,MAAM,EACN,gBAAgB,CAAC,MAAM,CAAC,GAAG,IAAI,CAChC,GACG,MAAM,GACN,oBAAoB,CAAC,MAAM,CAAC,SAAS,MAAM,cAAc,GACzD,YAAY,CAAC,cAAc,CAAC,SAAS,IAAI,GACvC,OAAO,CAAC;KAAG,CAAC,IAAI,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI;CAAE,EAAE,IAAI,CAAC,GACxE,OAAO,CAAC;KAAG,CAAC,IAAI,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC;CAAE,EAAE,IAAI,CAAC,GACnE,gBAAgB,CAAC,wCAAwC,CAAC,CAAA"}
{"version": 3, "file": "iso2022.js", "sourceRoot": "", "sources": ["../../src/encoding/iso2022.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAQhE,MAAM,QAAQ;IAAd;QACE,oBAAe,GAAe,EAAE,CAAC;IA0EnC,CAAC;IAxEC,IAAI;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,GAAY;QAchB,IAAI,CAAC,EAAE,CAAC,CAAC;QACT,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,UAAU,CAAC;QAGf,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC;QAC5B,MAAM,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC;QAE7B,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACpB,YAAY,EAAE,KACZ,IAAI,GAAG,CAAC,EACR,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAClC,IAAI,EAAE,EACN,CAAC;oBACD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAEvC,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM;wBAAE,SAAS,YAAY,CAAC;oBAEpD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;wBAC7B,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;4BAAE,SAAS,YAAY,CAAC;oBAEnD,IAAI,EAAE,CAAC;oBACP,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;oBACpB,SAAS,SAAS,CAAC;gBACrB,CAAC;gBAED,MAAM,EAAE,CAAC;YACX,CAAC;YAGD,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;gBAAE,MAAM,EAAE,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC;QAQ3B,UAAU,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;QAK3D,IAAI,IAAI,GAAG,MAAM,GAAG,CAAC;YAAE,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QAEhE,OAAO,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,eAAK,EAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACF;AAED,MAAa,WAAY,SAAQ,QAAQ;IAAzC;;QASE,oBAAe,GAAG;YAChB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;SACnB,CAAC;IACJ,CAAC;IAtBC,IAAI;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC;IACd,CAAC;CAgBF;AAvBD,kCAuBC;AAED,MAAa,WAAY,SAAQ,QAAQ;IAAzC;;QAOE,oBAAe,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAPC,IAAI;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,QAAQ;QACN,OAAO,IAAI,CAAC;IACd,CAAC;CAEF;AARD,kCAQC;AAED,MAAa,WAAY,SAAQ,QAAQ;IAAzC;;QAOE,oBAAe,GAAG;YAChB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACxB,CAAC,IAAI,EAAE,IAAI,CAAC;YACZ,CAAC,IAAI,EAAE,IAAI,CAAC;SACb,CAAC;IACJ,CAAC;IAnBC,IAAI;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,QAAQ;QACN,OAAO,IAAI,CAAC;IACd,CAAC;CAcF;AApBD,kCAoBC"}
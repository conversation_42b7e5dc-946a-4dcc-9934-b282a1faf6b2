{"name": "@inquirer/external-editor", "version": "1.0.1", "description": "Edit a string with the users preferred text editor using $VISUAL or $ENVIRONMENT", "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "module": "./dist/esm/index.js", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh", "editor", "external", "external-editor", "user", "visual"], "publishConfig": {"access": "public"}, "files": ["dist"], "engines": {"node": ">=18"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "sideEffects": false, "homepage": "https://github.com/SBoudrias/Inquirer.js/blob/main/packages/external-editor/README.md", "dependencies": {"chardet": "^2.1.0", "iconv-lite": "^0.6.3"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.2", "@types/chardet": "^1.0.0", "tshy": "^3.0.2"}, "peerDependencies": {"@types/node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}, "tshy": {"exclude": ["src/**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "scripts": {"tsc": "tshy", "attw": "attw --pack"}, "gitHead": "daf4d6f08a55ab0e0cca61d95ec0f579e7b3e698"}
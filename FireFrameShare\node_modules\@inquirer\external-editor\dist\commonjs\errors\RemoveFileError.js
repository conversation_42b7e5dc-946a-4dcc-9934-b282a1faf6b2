"use strict";
/***
 * Node External Editor
 *
 * <PERSON> <<EMAIL>>
 * MIT 2018
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveFileError = void 0;
class RemoveFileError extends Error {
    originalError;
    constructor(originalError) {
        super(`Failed to remove temporary file. ${originalError.message}`);
        this.originalError = originalError;
    }
}
exports.RemoveFileError = RemoveFileError;

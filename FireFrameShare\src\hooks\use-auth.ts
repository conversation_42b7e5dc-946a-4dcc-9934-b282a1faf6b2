
"use client";

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User } from '@/lib/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { email: string }) => void;
  signup: (details: { username: string, email: string }) => void;
  logout: () => void;
  setUser: (user: User | null) => void;
  updateUser: (user: Partial<User>) => void;
}

const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: true,
      login: (credentials) => {
        const user: User = { 
          username: credentials.email.split('@')[0], 
          email: credentials.email, 
          avatarUrl: 'https://placehold.co/100x100.png',
          bio: 'Welcome to my FireFrame profile!',
          contacts: {
            website: { value: 'https://example.com', isPublic: true },
            phone: { value: '************', isPublic: false },
            messaging: { platform: 'Telegram', username: '@username', isPublic: true },
          }
        };
        set({ user, isAuthenticated: true });
      },
      signup: (details) => {
        const user: User = { 
          username: details.username, 
          email: details.email, 
          avatarUrl: 'https://placehold.co/100x100.png',
          bio: 'Welcome to my FireFrame profile!',
          contacts: {
            website: { value: '', isPublic: false },
            phone: { value: '', isPublic: false },
            messaging: { platform: '', username: '', isPublic: false },
          }
        };
        set({ user, isAuthenticated: true });
      },
      logout: () => {
        set({ user: null, isAuthenticated: false });
      },
      setUser: (user) => set({ user, isAuthenticated: !!user, isLoading: false }),
      updateUser: (newDetails) => {
        const currentUser = get().user;
        if (currentUser) {
          set({ user: { ...currentUser, ...newDetails } });
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      onRehydrate: () => (state) => {
        if (state) {
          state.isLoading = false;
        }
      },
    }
  )
);

// Custom hook to initialize and use the store
export const useAuth = () => {
  const state = useAuthStore();

  // On initial load, we need to correctly set loading state.
  // The state from storage is rehydrated asynchronously.
  if (typeof window !== 'undefined' && state.isLoading) {
    const storedState = localStorage.getItem('auth-storage');
    if (storedState) {
        const parsed = JSON.parse(storedState);
        if(parsed.state.user) {
            useAuthStore.setState({ user: parsed.state.user, isAuthenticated: true, isLoading: false });
        } else {
            useAuthStore.setState({ isLoading: false });
        }
    } else {
        useAuthStore.setState({ isLoading: false });
    }
  }

  return state;
};

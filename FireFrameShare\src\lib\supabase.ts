import { createClient } from "@supabase/supabase-js";

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Debug environment variables
console.log("🔍 Environment Variables Debug:");
console.log("- NEXT_PUBLIC_SUPABASE_URL:", supabaseUrl ? "SET" : "UNDEFINED");
console.log(
  "- NEXT_PUBLIC_SUPABASE_ANON_KEY:",
  supabaseAnonKey ? "SET" : "UNDEFINED"
);
console.log(
  "- SUPABASE_SERVICE_ROLE_KEY:",
  supabaseServiceRoleKey ? "SET" : "UNDEFINED"
);

// Validate environment variables
if (!supabaseUrl) {
  throw new Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");
}

if (!supabaseAnonKey) {
  throw new Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");
}

// Create Supabase client for client-side operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Create Supabase client with service role for server-side operations
// Only use this for server-side operations that require elevated permissions
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Database and Storage references for convenience
export const db = supabase;
export const storage = supabase.storage;

// Log configuration (without sensitive data)
export const logSupabaseConfig = () => {
  console.log("Supabase Configuration:");
  console.log("- URL:", supabaseUrl);
  console.log("- Environment:", process.env.NODE_ENV);
  console.log("- Client initialized:", !!supabase);
};

// Initialize Supabase debugging
export const initSupabase = () => {
  console.log("🚀 Supabase initialized");
  logSupabaseConfig();

  // Test connection on initialization
  if (typeof window !== "undefined") {
    console.log("Client-side Supabase ready");
  } else {
    console.log("Server-side Supabase ready");
  }
};

export default supabase;

{"version": 3, "file": "unicode.js", "sourceRoot": "", "sources": ["../../src/encoding/unicode.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAMhE,MAAa,QAAQ;IACnB,IAAI;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,GAAY;QAChB,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;QAE3B,IACE,KAAK,CAAC,MAAM,IAAI,CAAC;YACjB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI;YACzB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EACzB,CAAC;YACD,OAAO,IAAA,eAAK,EAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAC/B,CAAC;QAGD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAnBD,4BAmBC;AAED,MAAa,QAAQ;IACnB,IAAI;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,GAAY;QAChB,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;QAE3B,IACE,KAAK,CAAC,MAAM,IAAI,CAAC;YACjB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI;YACzB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EACzB,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAE9D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAA,eAAK,EAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAC/B,CAAC;QAGD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAxBD,4BAwBC;AAMD,MAAM,MAAM;IACV,IAAI;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,MAAkB,EAAE,MAAc;QACxC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,GAAY;QAChB,IAAI,QAAQ,GAAG,CAAC,EACd,UAAU,GAAG,CAAC,EACd,MAAM,GAAG,KAAK,EACd,UAAU,GAAG,CAAC,CAAC;QACjB,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;QAE3B,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;YACzC,MAAM,GAAG,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAElC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,QAAQ,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC;gBAC/D,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,QAAQ,IAAI,CAAC,CAAC;YAChB,CAAC;QACH,CAAC;QAID,IAAI,MAAM,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YAC9B,UAAU,GAAG,GAAG,CAAC;QACnB,CAAC;aAAM,IAAI,MAAM,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAE,CAAC;YAChD,UAAU,GAAG,EAAE,CAAC;QAClB,CAAC;aAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YAC3C,UAAU,GAAG,GAAG,CAAC;QACnB,CAAC;aAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YAC3C,UAAU,GAAG,EAAE,CAAC;QAClB,CAAC;aAAM,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAE,CAAC;YAEtC,UAAU,GAAG,EAAE,CAAC;QAClB,CAAC;QAGD,OAAO,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,eAAK,EAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACF;AAED,MAAa,QAAS,SAAQ,MAAM;IAClC,IAAI;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IACD,OAAO,CAAC,KAAiB,EAAE,KAAa;QACtC,OAAO,CACL,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAC1B,CAAC;IACJ,CAAC;CACF;AAZD,4BAYC;AAED,MAAa,QAAS,SAAQ,MAAM;IAClC,IAAI;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,KAAiB,EAAE,KAAa;QACtC,OAAO,CACL,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAC1B,CAAC;IACJ,CAAC;CACF;AAbD,4BAaC"}
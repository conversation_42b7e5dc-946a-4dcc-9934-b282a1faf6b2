# Supabase Configuration
# Replace these placeholder values with your actual Supabase project credentials
# You can find these in your Supabase project dashboard under Settings > API

# Your Supabase project URL (e.g., https://your-project-id.supabase.co)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url

# Your Supabase anonymous/public key (safe to use in client-side code)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Your Supabase service role key (keep this secret, server-side only)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Optional: If you're using Supabase local development
# NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_local_anon_key

# Legacy Firebase Configuration (will be removed after migration)
# NEXT_PUBLIC_FIREBASE_PROJECT_ID=fireframe-cfl39
# NEXT_PUBLIC_USE_FIREBASE_EMULATORS=false
